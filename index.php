<?php
session_start();

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "product_comparison_platform";

$conn = new mysqli($servername, $username, $password, $dbname);

// Check if user is logged in and get user info
$isLoggedIn = isset($_SESSION['user_id']);
$userType = $isLoggedIn ? $_SESSION['user_type'] : null;
$userName = $isLoggedIn ? $_SESSION['username'] : null;

// Get current page from URL parameter
$currentPage = isset($_GET['page']) ? $_GET['page'] : 'home';

// Handle form submissions
$message = '';
$messageType = '';

// Contact form handling
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $currentPage == 'contact') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $subject = trim($_POST['subject']);
    $contactMessage = trim($_POST['message']);
    $userType = $_POST['user_type'];

    // Basic validation
    if (empty($name) || empty($email) || empty($subject) || empty($contactMessage)) {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    } else {
        $message = 'Thank you for contacting us! We will get back to you within 24 hours.';
        $messageType = 'success';

        // Clear form data after successful submission
        if ($messageType == 'success') {
            $name = $email = $subject = $contactMessage = $userType = '';
        }
    }
}

// Sign in form handling
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $currentPage == 'signin') {
    $username = htmlspecialchars(trim($_POST['username']));
    $password = $_POST['password'];

    if (!empty($username) && !empty($password)) {
        // Check user in the unified users table using username
        $stmt = $conn->prepare("SELECT user_id, username, email, password_hash, user_type, status FROM users WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();

            if (password_verify($password, $user['password_hash'])) {
                // Set session variables
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_type'] = $user['user_type'];

                $message = 'Login successful! Redirecting...';
                $messageType = 'success';

                // Redirect based on user type
                if ($user['user_type'] == 'admin') {
                    header("refresh:2;url=admin_dashboard.php");
                } elseif ($user['user_type'] == 'seller') {
                    header("refresh:2;url=seller_dashboard.php");
                } else {
                    header("refresh:2;url=customer_dashboard.php");
                }
            } else {
                $message = 'Invalid username or password.';
                $messageType = 'error';
            }
        } else {
            $message = 'Invalid username or password.';
            $messageType = 'error';
        }
    } else {
        $message = 'Please fill in all fields.';
        $messageType = 'error';
    }
}

// Customer signup form handling
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $currentPage == 'signup') {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $date_of_birth = $_POST['date_of_birth'];
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validation
    if (empty($first_name) || empty($last_name) || empty($email) || empty($username) || empty($password)) {
        $message = "Please fill in all required fields.";
        $messageType = "error";
    } elseif ($password !== $confirm_password) {
        $message = "Passwords do not match.";
        $messageType = "error";
    } elseif (strlen($password) < 6) {
        $message = "Password must be at least 6 characters long.";
        $messageType = "error";
    } else {
        // Check if username or email already exists
        $check_query = "SELECT user_id FROM users WHERE username = ? OR email = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("ss", $username, $email);
        $check_stmt->execute();
        $result = $check_stmt->get_result();

        if ($result->num_rows > 0) {
            $message = "Username or email already exists.";
            $messageType = "error";
        } else {
            // Create account
            $password_hash = password_hash($password, PASSWORD_DEFAULT);

            try {
                $conn->begin_transaction();

                // Insert into users table
                $user_query = "INSERT INTO users (username, email, password_hash, user_type, status) VALUES (?, ?, ?, 'customer', 'active')";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("sss", $username, $email, $password_hash);
                $user_stmt->execute();

                $user_id = $conn->insert_id;

                // Insert into customer_profiles table
                $profile_query = "INSERT INTO customer_profiles (customer_id, first_name, last_name, phone_number, address, date_of_birth) VALUES (?, ?, ?, ?, ?, ?)";
                $profile_stmt = $conn->prepare($profile_query);
                $profile_stmt->bind_param("isssss", $user_id, $first_name, $last_name, $phone, $address, $date_of_birth);
                $profile_stmt->execute();

                $conn->commit();

                $message = "Account created successfully! You can now sign in.";
                $messageType = "success";

            } catch (Exception $e) {
                $conn->rollback();
                $message = "Error creating account. Please try again.";
                $messageType = "error";
            }
        }
    }
}

// Function to get available images
function getAvailableImages() {
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'JPG', 'JPEG', 'PNG', 'GIF'];
    $images = [];

    $files = scandir('.');
    foreach ($files as $file) {
        $extension = pathinfo($file, PATHINFO_EXTENSION);
        if (in_array($extension, $imageExtensions) && $file !== '.' && $file !== '..') {
            $images[] = $file;
        }
    }

    return $images;
}

$availableImages = getAvailableImages();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php
        switch($currentPage) {
            case 'about': echo 'About Us - Product Price Comparison Platform'; break;
            case 'contact': echo 'Contact Us - Product Price Comparison Platform'; break;
            case 'signin': echo 'Sign In - Product Price Comparison Platform'; break;
            case 'signup': echo 'Sign Up - Product Price Comparison Platform'; break;
            default: echo 'Product Comparison Platform - Smart Shopping Made Easy'; break;
        }
    ?></title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: white;
            color: black;
            line-height: 1.6;
        }

        /* Header Styles */
        header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .logo::before {
            content: "🛒";
            margin-right: 0.5rem;
            font-size: 2rem;
        }

        /* Search Bar */
        .search-container {
            flex: 1;
            max-width: 500px;
            margin: 0 2rem;
            position: relative;
        }

        .search-bar {
            width: 100%;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #4CAF50;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .search-btn:hover {
            background: #45a049;
        }

        /* Navigation */
        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }

        nav a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }

        nav a:hover {
            background: rgba(255,255,255,0.2);
        }

        /* Mobile Navigation */
        .mobile-menu {
            display: none;
            cursor: pointer;
            font-size: 1.5rem;
            color: white;
        }

        /* Hero Section with Slideshow */
        .hero {
            position: relative;
            height: 500px;
            overflow: hidden;
            background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
        }

        .slideshow-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .slide {
            display: none;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .slide.active {
            display: block;
        }

        .slide-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 2;
            max-width: 600px;
            padding: 2rem;
        }

        .slide-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1;
        }

        .slide h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .slide p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .cta-button {
            background: #4CAF50;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .cta-button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        /* Navigation Dots */
        .dots-container {
            text-align: center;
            position: absolute;
            bottom: 20px;
            width: 100%;
            z-index: 3;
        }

        .dot {
            cursor: pointer;
            height: 15px;
            width: 15px;
            margin: 0 5px;
            background-color: rgba(255,255,255,0.5);
            border-radius: 50%;
            display: inline-block;
            transition: background-color 0.3s;
        }

        .dot.active, .dot:hover {
            background-color: white;
        }

        /* Image Gallery Section */
        .image-gallery {
            padding: 4rem 2rem;
            background: #f8f9fa;
        }

        .gallery-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .gallery-container h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #4CAF50;
        }

        .gallery-container p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 3rem;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            background: white;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(76, 175, 80, 0.2);
        }

        .gallery-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .gallery-item:hover img {
            transform: scale(1.05);
        }

        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 1.5rem;
            transform: translateY(100%);
            transition: transform 0.3s;
        }

        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }

        .gallery-overlay h4 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .gallery-overlay p {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Features Section */
        .features {
            padding: 4rem 2rem;
            background: white;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .features h2 {
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #4CAF50;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(76, 175, 80, 0.2);
            border-color: #4CAF50;
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #4CAF50;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: black;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* About Us Page Styles */
        .hero-about {
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            color: white;
            text-align: center;
            padding: 4rem 2rem;
            border-radius: 15px;
            margin-bottom: 3rem;
            animation: fadeInUp 1s ease-out;
        }

        .hero-about h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-about p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .content-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: slideInUp 0.8s ease-out;
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .content-card h3 {
            color: #4CAF50;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .content-card .icon {
            font-size: 2rem;
        }

        .feature-item {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 25px rgba(76, 175, 80, 0.2);
        }

        .feature-item .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #4CAF50;
        }

        .values-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 3rem;
            border-radius: 15px;
            margin: 3rem 0;
        }

        .values-section h2 {
            color: #4CAF50;
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .value-item {
            text-align: center;
            padding: 1.5rem;
        }

        .value-item .icon {
            font-size: 3rem;
            color: #4CAF50;
            margin-bottom: 1rem;
        }

        .value-item h4 {
            color: #2E7D32;
            margin-bottom: 0.5rem;
        }

        .statistics {
            background: #4CAF50;
            color: white;
            padding: 3rem 2rem;
            border-radius: 15px;
            margin: 3rem 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            color: white;
        }

        .stat-item p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .cta-section {
            background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 100%);
            color: white;
            text-align: center;
            padding: 3rem 2rem;
            border-radius: 15px;
            margin: 3rem 0;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .cta-section p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.2rem;
            background: white;
            color: #4CAF50;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        /* Contact Page Styles */
        .contact-hero {
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            color: white;
            text-align: center;
            padding: 4rem 2rem;
            border-radius: 15px;
            margin-bottom: 3rem;
        }

        .contact-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .contact-hero p {
            font-size: 1.3rem;
            opacity: 0.9;
        }

        .contact-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 3rem;
            margin: 3rem 0;
        }

        .contact-info h3 {
            color: #4CAF50;
            font-size: 1.8rem;
            margin-bottom: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .contact-icon {
            font-size: 2rem;
            color: #4CAF50;
        }

        .contact-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .contact-item p {
            color: #666;
            margin: 0;
        }

        .contact-form {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .contact-form h3 {
            color: #4CAF50;
            font-size: 1.8rem;
            margin-bottom: 2rem;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            flex: 1;
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .btn-submit {
            background: #4CAF50;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-submit:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        /* Auth Pages Styles */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 70vh;
            padding: 2rem;
        }

        .auth-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
        }

        .signup-card {
            max-width: 700px;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-header h1 {
            color: #2E7D32;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .auth-header p {
            color: #666;
            font-size: 1.1rem;
        }

        .auth-form {
            margin-bottom: 2rem;
        }

        .btn-auth {
            background: #4CAF50;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-auth:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .auth-links {
            text-align: center;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }

        .auth-links p {
            color: #666;
        }

        .auth-links a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: bold;
        }

        .auth-links a:hover {
            text-decoration: underline;
        }

        /* Message Styles */
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Footer */
        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem;
            margin-top: 4rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                display: none;
                flex-direction: column;
                width: 100%;
                background: #4CAF50;
                position: absolute;
                top: 100%;
                left: 0;
                padding: 1rem;
            }

            nav ul.active {
                display: flex;
            }

            .mobile-menu {
                display: block;
            }

            .search-container {
                margin: 0;
                order: 2;
            }

            .slide h2 {
                font-size: 2rem;
            }

            .slide p {
                font-size: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .gallery-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .contact-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .hero-about h1 {
                font-size: 2rem;
            }

            .hero-about p {
                font-size: 1.1rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .values-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .auth-card {
                padding: 2rem;
                margin: 1rem;
            }

            .auth-header h1 {
                font-size: 1.8rem;
            }

            .contact-hero h1 {
                font-size: 2rem;
            }

            .contact-hero p {
                font-size: 1.1rem;
            }
        }

        /* Footer Styles */
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .footer p {
            margin: 0.5rem 0;
        }

        .social-links {
            margin-top: 1rem;
        }

        .social-links a {
            color: white;
            text-decoration: none;
            margin: 0 0.5rem;
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }

        .social-links a:hover {
            transform: scale(1.2);
        }

        /* User welcome message */
        .user-welcome {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 0.3rem 0.8rem !important;
            font-size: 0.9rem;
        }

        .user-welcome:hover {
            background: rgba(255,255,255,0.3) !important;
        }

        /* Cart badge */
        .cart-badge {
            background: #ff4444;
            color: white;
            border-radius: 50%;
            padding: 0.2rem 0.5rem;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Slide Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide.active {
            animation: slideIn 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <a href="#" class="logo">HealthMarket</a>
            
            <div class="search-container">
                <input type="text" class="search-bar" placeholder="Search for products, supplements, boosters..." id="searchInput">
                <button class="search-btn" onclick="performSearch()">🔍</button>
            </div>
            
            <nav>
                <ul id="navMenu">
                    <li><a href="?page=home" class="nav-link <?php echo $currentPage == 'home' ? 'active' : ''; ?>">Home</a></li>
                    <li><a href="?page=about" class="nav-link <?php echo $currentPage == 'about' ? 'active' : ''; ?>">About Us</a></li>
                    <li><a href="?page=contact" class="nav-link <?php echo $currentPage == 'contact' ? 'active' : ''; ?>">Contact Us</a></li>

                    <?php if ($isLoggedIn): ?>
                        <!-- Show user-specific navigation based on role -->
                        <?php if ($userType == 'admin'): ?>
                            <li><a href="admin_dashboard.php" class="nav-link">Admin Panel</a></li>
                        <?php elseif ($userType == 'seller'): ?>
                            <li><a href="seller_dashboard.php" class="nav-link">Seller Dashboard</a></li>
                        <?php elseif ($userType == 'customer'): ?>
                            <li><a href="customer_dashboard.php" class="nav-link">Dashboard</a></li>
                        <?php endif; ?>

                        <li><a href="#" class="nav-link user-welcome">Welcome, <?php echo htmlspecialchars($userName); ?>!</a></li>
                        <li><a href="logout.php" class="nav-link">Logout</a></li>
                    <?php else: ?>
                        <!-- Show sign up/sign in for non-logged users -->
                        <li><a href="?page=signup" class="nav-link <?php echo $currentPage == 'signup' ? 'active' : ''; ?>">Sign Up</a></li>
                        <li><a href="?page=signin" class="nav-link <?php echo $currentPage == 'signin' ? 'active' : ''; ?>">Sign In</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="mobile-menu" onclick="toggleMobileMenu()">☰</div>
        </div>
    </header>

    <!-- Dynamic Content Area -->
    <main class="main-content">
        <?php if ($currentPage == 'home' || $currentPage == ''): ?>
            <!-- HOME PAGE CONTENT -->
            <!-- Hero Section with Slideshow -->
            <section class="hero">
                <div class="slideshow-container">
                    <!-- Slide 1 -->
                    <div class="slide active" style="background: linear-gradient(135deg, #4CAF50, #45a049);">
                        <div class="slide-overlay"></div>
                        <div class="slide-content">
                            <h2>Compare & Choose Smart</h2>
                            <p>Find the best health products from multiple vendors. Compare prices, check nutrition, and make informed decisions for your wellness journey.</p>
                            <a href="?page=signup" class="cta-button">Get Started</a>
                        </div>
                    </div>

                    <!-- Slide 2 -->
                    <div class="slide" style="background: linear-gradient(135deg, #2196F3, #1976D2);">
                        <div class="slide-overlay"></div>
                        <div class="slide-content">
                            <h2>Nutritional Balance Checker</h2>
                            <p>Our smart system analyzes your cart and suggests products to ensure you get all essential nutrients for optimal health.</p>
                            <a href="?page=signup" class="cta-button">Start Shopping</a>
                        </div>
                    </div>

                    <!-- Slide 3 -->
                    <div class="slide" style="background: linear-gradient(135deg, #FF9800, #F57C00);">
                        <div class="slide-overlay"></div>
                        <div class="slide-content">
                            <h2>Trusted Sellers Network</h2>
                            <p>Shop from verified sellers across multiple markets. Quality products, competitive prices, and reliable delivery guaranteed.</p>
                            <a href="?page=signup" class="cta-button">Join as Seller</a>
                        </div>
                    </div>
                </div>

                <!-- Navigation Dots -->
                <div class="dots-container">
                    <span class="dot active" onclick="currentSlide(1)"></span>
                    <span class="dot" onclick="currentSlide(2)"></span>
                    <span class="dot" onclick="currentSlide(3)"></span>
                </div>
            </section>

            <!-- Dynamic Image Gallery -->
            <section class="image-gallery">
                <div class="gallery-container">
                    <h2>Our Products</h2>
                    <p>Fresh and quality products available on our platform</p>
                    <div class="gallery-grid">
                        <?php foreach($availableImages as $image): ?>
                            <div class="gallery-item">
                                <img src="<?php echo htmlspecialchars($image); ?>" alt="Product Image" loading="lazy">
                                <div class="gallery-overlay">
                                    <h4><?php echo ucfirst(pathinfo($image, PATHINFO_FILENAME)); ?></h4>
                                    <p>Available on our platform</p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="features">
                <div class="features-container">
                    <h2>Why Choose Our Platform?</h2>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">🔍</div>
                            <h3>Smart Product Comparison</h3>
                            <p>Compare similar products from different sellers to find the best deals. See prices, quality ratings, and nutritional values side by side.</p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">🥗</div>
                            <h3>Nutritional Balance</h3>
                            <p>Our intelligent system checks your cart for nutritional completeness and suggests products to fill any gaps in your diet.</p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">🏥</div>
                            <h3>Health Categories</h3>
                            <p>Browse energy boosters, body-building supplements, and disease prevention products from verified sellers.</p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">🛒</div>
                            <h3>Easy Ordering</h3>
                            <p>Simple cart system with secure payment options including MomoPay and Mobile Money. Upload payment proof for quick verification.</p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">🏪</div>
                            <h3>Multiple Markets</h3>
                            <p>Access products from various markets and vendors in one place. Compare prices and choose the best option for you.</p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">📱</div>
                            <h3>Mobile Friendly</h3>
                            <p>Shop on any device with our responsive design. Perfect for busy lifestyles and on-the-go shopping.</p>
                        </div>
                    </div>
                </div>
            </section>
        <?php elseif ($currentPage == 'about'): ?>
            <!-- ABOUT US PAGE CONTENT -->
            <section class="hero-about">
                <h1>About Our Platform</h1>
                <p>Empowering customers with smart product comparison and helping vendors reach more customers through our comprehensive e-commerce ecosystem.</p>
            </section>

            <!-- Mission & Vision -->
            <div class="content-grid">
                <div class="content-card">
                    <h3><span class="icon">🎯</span>Our Mission</h3>
                    <p>To revolutionize online shopping by providing customers with comprehensive product comparison tools and nutritional guidance, while empowering sellers with robust vendor management systems. We aim to make informed purchasing decisions accessible to everyone.</p>
                </div>

                <div class="content-card">
                    <h3><span class="icon">👁️</span>Our Vision</h3>
                    <p>To become the leading product price comparison platform that bridges the gap between customers seeking quality products and sellers offering exceptional value, creating a transparent and efficient marketplace ecosystem.</p>
                </div>

                <div class="content-card">
                    <h3><span class="icon">💡</span>Our Innovation</h3>
                    <p>We integrate advanced nutritional analysis with price comparison, ensuring customers not only find the best deals but also make balanced nutritional choices for their health and wellness goals.</p>
                </div>
            </div>

            <!-- Platform Features -->
            <section class="features-section">
                <h2 style="text-align: center; color: #4CAF50; font-size: 2.5rem; margin-bottom: 2rem;">Platform Features</h2>
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="icon">🔍</div>
                        <h4>Smart Product Search</h4>
                        <p>Advanced search and filtering system to find exactly what you need</p>
                    </div>

                    <div class="feature-item">
                        <div class="icon">💰</div>
                        <h4>Price Comparison</h4>
                        <p>Compare prices across multiple vendors to get the best deals</p>
                    </div>

                    <div class="feature-item">
                        <div class="icon">🥗</div>
                        <h4>Nutritional Balance</h4>
                        <p>AI-powered system checks for balanced nutrition in your cart</p>
                    </div>

                    <div class="feature-item">
                        <div class="icon">🛒</div>
                        <h4>Smart Shopping Cart</h4>
                        <p>Intelligent cart that suggests complementary products</p>
                    </div>

                    <div class="feature-item">
                        <div class="icon">📱</div>
                        <h4>Mobile Payment</h4>
                        <p>Secure payment through MomoPay and Mobile Money</p>
                    </div>

                    <div class="feature-item">
                        <div class="icon">📊</div>
                        <h4>Vendor Analytics</h4>
                        <p>Comprehensive reporting and inventory management for sellers</p>
                    </div>
                </div>
            </section>

            <!-- Our Values -->
            <section class="values-section">
                <h2>Our Core Values</h2>
                <div class="values-grid">
                    <div class="value-item">
                        <div class="icon">🤝</div>
                        <h4>Trust</h4>
                        <p>Building reliable relationships between customers and vendors</p>
                    </div>
                    <div class="value-item">
                        <div class="icon">🚀</div>
                        <h4>Innovation</h4>
                        <p>Continuously improving our platform with cutting-edge technology</p>
                    </div>
                    <div class="value-item">
                        <div class="icon">⭐</div>
                        <h4>Quality</h4>
                        <p>Ensuring only high-quality products and services on our platform</p>
                    </div>
                    <div class="value-item">
                        <div class="icon">🌍</div>
                        <h4>Accessibility</h4>
                        <p>Making quality products accessible to everyone, everywhere</p>
                    </div>
                </div>
            </section>

            <!-- Statistics -->
            <section class="statistics">
                <h2 style="text-align: center; margin-bottom: 2rem;">Platform Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <h3 id="productsCount">1,250+</h3>
                        <p>Products Available</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="vendorsCount">150+</h3>
                        <p>Registered Vendors</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="customersCount">5,000+</h3>
                        <p>Happy Customers</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="ordersCount">10,000+</h3>
                        <p>Orders Completed</p>
                    </div>
                </div>
            </section>

            <!-- Call to Action -->
            <section class="cta-section">
                <h2>Ready to Get Started?</h2>
                <p>Join thousands of customers and vendors who trust our platform for their e-commerce needs</p>
                <a href="?page=signup" class="btn-large">Join Our Platform Today</a>
            </section>

        <?php elseif ($currentPage == 'contact'): ?>
            <!-- CONTACT PAGE CONTENT -->
            <section class="contact-hero">
                <h1>Contact Us</h1>
                <p>We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
            </section>

            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <div class="contact-container">
                <div class="contact-info">
                    <h3>Get in Touch</h3>
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div>
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div>
                            <h4>Phone</h4>
                            <p>+250 123 456 789</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div>
                            <h4>Address</h4>
                            <p>Kigali, Rwanda</p>
                        </div>
                    </div>
                </div>

                <div class="contact-form">
                    <h3>Send us a Message</h3>
                    <form method="POST" action="?page=contact">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" required
                                       value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="subject">Subject *</label>
                                <input type="text" id="subject" name="subject" required
                                       value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>">
                            </div>
                            <div class="form-group">
                                <label for="user_type">I am a:</label>
                                <select id="user_type" name="user_type">
                                    <option value="customer" <?php echo (isset($_POST['user_type']) && $_POST['user_type'] == 'customer') ? 'selected' : ''; ?>>Customer</option>
                                    <option value="seller" <?php echo (isset($_POST['user_type']) && $_POST['user_type'] == 'seller') ? 'selected' : ''; ?>>Seller</option>
                                    <option value="other" <?php echo (isset($_POST['user_type']) && $_POST['user_type'] == 'other') ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" rows="6" required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                        </div>

                        <button type="submit" class="btn-submit">Send Message</button>
                    </form>
                </div>
            </div>

        <?php elseif ($currentPage == 'signin'): ?>
            <!-- SIGN IN PAGE CONTENT -->
            <section class="auth-container">
                <div class="auth-card">
                    <div class="auth-header">
                        <h1>Welcome Back</h1>
                        <p>Sign in to your account to continue</p>
                    </div>

                    <?php if (!empty($message)): ?>
                        <div class="message <?php echo $messageType; ?>">
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="?page=signin" class="auth-form">
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" name="username" required
                                   value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required>
                        </div>

                        <div class="form-options">
                            <div class="remember-me">
                                <input type="checkbox" id="remember" name="remember">
                                <label for="remember">Remember me</label>
                            </div>
                        </div>

                        <button type="submit" class="btn-auth">Sign In</button>
                    </form>

                    <div class="auth-links">
                        <p>Don't have an account? <a href="?page=signup">Sign up here</a></p>
                    </div>
                </div>
            </section>

        <?php elseif ($currentPage == 'signup'): ?>
            <!-- SIGN UP PAGE CONTENT -->
            <section class="auth-container">
                <div class="auth-card signup-card">
                    <div class="auth-header">
                        <h1>Create Account</h1>
                        <p>Join our platform to start shopping</p>
                    </div>

                    <?php if (!empty($message)): ?>
                        <div class="message <?php echo $messageType; ?>">
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="?page=signup" class="auth-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="first_name">First Name *</label>
                                <input type="text" id="first_name" name="first_name" required
                                       value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                            </div>
                            <div class="form-group">
                                <label for="last_name">Last Name *</label>
                                <input type="text" id="last_name" name="last_name" required
                                       value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone"
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="address">Address</label>
                            <input type="text" id="address" name="address"
                                   value="<?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?>">
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="date_of_birth">Date of Birth</label>
                                <input type="date" id="date_of_birth" name="date_of_birth"
                                       value="<?php echo isset($_POST['date_of_birth']) ? htmlspecialchars($_POST['date_of_birth']) : ''; ?>">
                            </div>
                            <div class="form-group">
                                <label for="username">Username *</label>
                                <input type="text" id="username" name="username" required
                                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="password">Password *</label>
                                <input type="password" id="password" name="password" required minlength="6">
                            </div>
                            <div class="form-group">
                                <label for="confirm_password">Confirm Password *</label>
                                <input type="password" id="confirm_password" name="confirm_password" required minlength="6">
                            </div>
                        </div>

                        <button type="submit" class="btn-auth">Create Account</button>
                    </form>

                    <div class="auth-links">
                        <p>Already have an account? <a href="?page=signin">Sign in here</a></p>
                    </div>
                </div>
            </section>

        <?php endif; ?>

 <!-- Footer -->
    <footer class="footer">
        <p>&copy; 2024 Product Price Comparison Platform. All rights reserved.</p>
        <p>Empowering Smart Shopping Decisions</p>
        <div class="social-links">
            <a href="#" title="Facebook">📘</a>
            <a href="#" title="Twitter">🐦</a>
            <a href="#" title="Instagram">📷</a>
            <a href="#" title="LinkedIn">💼</a>
        </div>
    </footer>

    <script>
        // Slideshow functionality
        let slideIndex = 1;
        let slideInterval;

        function showSlides(n) {
            let slides = document.getElementsByClassName("slide");
            let dots = document.getElementsByClassName("dot");
            
            if (n > slides.length) {slideIndex = 1}
            if (n < 1) {slideIndex = slides.length}
            
            for (let i = 0; i < slides.length; i++) {
                slides[i].classList.remove("active");
            }
            
            for (let i = 0; i < dots.length; i++) {
                dots[i].classList.remove("active");
            }
            
            slides[slideIndex-1].classList.add("active");
            dots[slideIndex-1].classList.add("active");
        }

        function currentSlide(n) {
            clearInterval(slideInterval);
            showSlides(slideIndex = n);
            autoSlide();
        }

        function nextSlide() {
            showSlides(slideIndex += 1);
        }

        function autoSlide() {
            slideInterval = setInterval(nextSlide, 4000);
        }

        // Mobile menu toggle
        function toggleMobileMenu() {
            const navMenu = document.getElementById('navMenu');
            navMenu.classList.toggle('active');
        }

        // Search functionality with PHP integration
        function performSearch() {
            const searchInput = document.getElementById('searchInput');
            const query = searchInput.value.trim();

            if (query) {
                // Show loading state
                const searchBtn = document.querySelector('.search-btn');
                const originalText = searchBtn.innerHTML;
                searchBtn.innerHTML = '<div class="loading"></div>';

                // For now, show alert with search query (can be enhanced later)
                setTimeout(() => {
                    alert(`Searching for: "${query}"\n\nThis feature will be enhanced in the products page.`);
                    searchBtn.innerHTML = originalText;
                }, 500);
            } else {
                alert('Please enter a search term');
            }
        }

        // PHP session check for cart functionality
        function checkLoginStatus() {
            <?php if ($isLoggedIn): ?>
                return true;
            <?php else: ?>
                return false;
            <?php endif; ?>
        }

        // Handle navigation based on user status
        function handleNavigation(page) {
            const needsLogin = ['cart.php', 'checkout.php', 'profile.php'];
            
            if (needsLogin.some(p => page.includes(p)) && !checkLoginStatus()) {
                alert('Please sign in to access this feature');
                window.location.href = 'signin.php';
                return false;
            }
            
            return true;
        }

        // Enter key search
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Navigation active state
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                // Remove active class from all links
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                // Add active class to clicked link
                this.classList.add('active');
            });
        });

        // Smooth scrolling for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Initialize slideshow
        document.addEventListener('DOMContentLoaded', function() {
            // Only initialize slideshow on home page
            const slides = document.getElementsByClassName("slide");
            if (slides.length > 0) {
                showSlides(slideIndex);
                autoSlide();
            }

            // Add some interactive effects
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add form validation for signup page
            const signupForm = document.querySelector('form[action*="signup"]');
            if (signupForm) {
                signupForm.addEventListener('submit', function(e) {
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirm_password').value;

                    if (password !== confirmPassword) {
                        e.preventDefault();
                        alert('Passwords do not match!');
                        return false;
                    }

                    if (password.length < 6) {
                        e.preventDefault();
                        alert('Password must be at least 6 characters long!');
                        return false;
                    }
                });
            }

            // Add form validation for contact page
            const contactForm = document.querySelector('form[action*="contact"]');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    const name = document.getElementById('name').value.trim();
                    const email = document.getElementById('email').value.trim();
                    const subject = document.getElementById('subject').value.trim();
                    const message = document.getElementById('message').value.trim();

                    if (!name || !email || !subject || !message) {
                        e.preventDefault();
                        alert('Please fill in all required fields!');
                        return false;
                    }
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('navMenu').classList.remove('active');
            }
        });

        // Add loading animation to CTA buttons
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // Don't prevent default for actual navigation
                const originalText = this.innerHTML;
                this.innerHTML = originalText + ' <div class="loading" style="display: inline-block; margin-left: 10px;"></div>';
                
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        });

        // Console welcome message with PHP integration
        console.log('🛒 Welcome to HealthMarket Platform!');
        console.log('🔍 Smart product comparison and nutritional balance checking');
        console.log('💚 Making healthy choices accessible and affordable');
        
        <?php if ($isLoggedIn): ?>
            console.log(`👋 Welcome back, <?php echo $userName; ?>! (<?php echo ucfirst($userType); ?>)`);
        <?php else: ?>
            console.log('🔐 Sign in to unlock full platform features');
        <?php endif; ?>
    </script>
</body>
</html>